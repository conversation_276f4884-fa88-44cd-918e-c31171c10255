@charset "UTF-8";

body,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
input,
p,
blockquote,
th,
td {
  font-weight: 400;
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
  color: var(--art-text-gray-800);
}

body {
  color: var(--art-text-gray-700);
  text-align: left;
  font-family:
    Inter, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', Arial, sans-serif;
}

select {
  font-size: 12px;
}

table {
  border-collapse: collapse;
}

fieldset,
img {
  border: 0 none;
}

fieldset {
  margin: 0;
  padding: 0;
}

fieldset p {
  margin: 0;
  padding: 0 0 0 8px;
}

legend {
  display: none;
}

address,
caption,
em,
strong,
th,
i {
  font-style: normal;
  font-weight: 400;
}

table caption {
  margin-left: -1px;
}

hr {
  border-bottom: 1px solid #ffffff;
  border-top: 1px solid #e4e4e4;
  border-width: 1px 0;
  clear: both;
  height: 2px;
  margin: 5px 0;
  overflow: hidden;
}

ol,
ul {
  list-style-image: none;
  list-style-position: outside;
  list-style-type: none;
}

caption,
th {
  text-align: left;
}

q:before,
q:after,
blockquote:before,
blockquote:after {
  content: ””;
}

/*滚动条*/
/*滚动条整体部分,必须要设置*/
::-webkit-scrollbar {
  width: 8px !important;
  height: 0 !important;
}

/*滚动条的轨道*/
::-webkit-scrollbar-track {
  background-color: var(--art-text-gray-100);
}

/*滚动条的滑块按钮*/
::-webkit-scrollbar-thumb {
  border-radius: 5px;
  background-color: #cccccc !important;
  transition: all 0.2s;
  -webkit-transition: all 0.2s;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #b0abab !important;
}

/*滚动条的上下两端的按钮*/
::-webkit-scrollbar-button {
  height: 0px;
  width: 0;
}

.dark {
  ::-webkit-scrollbar-track {
    background-color: var(--art-bg-color);
  }

  ::-webkit-scrollbar-thumb {
    background-color: rgba(var(--art-gray-300-rgb), 0.8) !important;
  }
}
